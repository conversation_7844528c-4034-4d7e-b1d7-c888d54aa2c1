services:
  app_db_pg:
    image: postgres:15
    container_name: dhruva-platform-app-db-pg
    environment:
      - POSTGRES_USER=${POSTGRES_APP_DB_USERNAME}
      - POSTGRES_PASSWORD=${POSTGRES_APP_DB_PASSWORD}
      - POSTGRES_DB=${POSTGRES_APP_DB_NAME}
    volumes:
      - app_db_pg_data:/var/lib/postgresql/data
      - ./postgresql_schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_APP_DB_USERNAME} -d ${POSTGRES_APP_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dhruva-network

  log_db_pg:
    image: postgres:15
    container_name: dhruva-platform-log-db-pg
    environment:
      - POSTGRES_USER=${POSTGRES_LOG_DB_USERNAME}
      - POSTGRES_PASSWORD=${POSTGRES_LOG_DB_PASSWORD}
      - POSTGRES_DB=${POSTGRES_LOG_DB_NAME}
    volumes:
      - log_db_pg_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_LOG_DB_USERNAME} -d ${POSTGRES_LOG_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dhruva-network

  redis:
    image: redis:7.2.4
    container_name: dhruva-platform-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dhruva-network

  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: dhruva-platform-timescaledb
    environment:
      - POSTGRES_USER=${TIMESCALE_USER}
      - POSTGRES_PASSWORD=${TIMESCALE_PASSWORD}
      - POSTGRES_DB=${TIMESCALE_DATABASE_NAME}
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
    ports:
      - "${TIMESCALE_PORT}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${TIMESCALE_USER} -d ${TIMESCALE_DATABASE_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - dhruva-network

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: dhruva-platform-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "8082:80"
    depends_on:
      - app_db_pg
      - log_db_pg
    networks:
      - dhruva-network

networks:
  dhruva-network:
    name: dhruva-network

volumes:
  timescaledb_data:
  app_db_pg_data:
  log_db_pg_data:
  redis_data: